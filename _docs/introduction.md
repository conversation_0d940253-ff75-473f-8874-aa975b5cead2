# Introduction to Drops.Relation

Drops.Relation is a high-level relation abstraction with extensible architecture built on top of Ecto.SQL, created to accelerate Ecto development in both early stages and as applications scale.

## Architecture Overview

```mermaid
graph TD
    %% Main Relation Module
    relationModule["MyApp.Users<br/>Relation Module"]

    %% Schema Subgraph
    subgraph Schema ["Schema"]
        direction TB
        schema["Drops.Relation.Schema<br/>id: :integer<br/>name: :string<br/>email: :string"]
        dbTable[("users table")]
        cache@{ shape: lin-cyl, label: "Schema Cache" }
        cache-comment@{ shape: braces, label: "priv/repo/schemas" }

        schema -.-> dbTable
        schema --> cache
        cache -.-> cache-comment
    end

    %% Ecto Integration Subgraph
    subgraph EctoIntegration ["Ecto Integration"]
        direction TB
        ectoSchema["MyApp.Users.User<br/>(Ecto Schema)"]
        queryable{{"Ecto.Queryable.MyApp.Users"}}
    end

    %% Protocols Subgraph
    subgraph Protocols ["Protocols"]
        direction TB
        enumerable{{"Enumerable.MyApp.Users"}}
        loadable{{"Drops.Relation.Loadable.MyApp.Users"}}
    end

    %% Connections
    relationModule --> Schema
    relationModule --> EctoIntegration
    relationModule --> Protocols

    %% Styling
    classDef relationModuleStyle fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000
    classDef schemaStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef ectoStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000
    classDef protocolStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef dbStyle fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#000
    classDef cacheStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px,color:#000

    class relationModule relationModuleStyle
    class schema,dbTable,cache,cache-comment schemaStyle
    class ectoSchema,queryable ectoStyle
    class enumerable,loadable protocolStyle

    %% Subgraph styling
    style Schema fill:#f8f4ff,stroke:#673ab7,stroke-width:2px
    style EctoIntegration fill:#f1f8e9,stroke:#4caf50,stroke-width:2px
    style Protocols fill:#fff8e1,stroke:#ff9800,stroke-width:2px
```

## Ecto vs Drops.Relation Comparison

### Schema Definition

<!-- tabs-open -->

### Plain Ecto

```elixir
defmodule MyApp.User do
  use Ecto.Schema

  schema "users" do
    field :name, :string
    field :email, :string
    field :active, :boolean
    timestamps()
  end
end
```

### Drops.Relation

```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true)
end
```

<!-- tabs-close -->

### Schema with Custom Fields

<!-- tabs-open -->

### Plain Ecto

```elixir
defmodule MyApp.User do
  use Ecto.Schema

  schema "users" do
    field :name, :string
    field :email, :string
    field :active, :boolean, default: true
    field :role, :string, default: "member"
    timestamps()
  end
end
```

### Drops.Relation

```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true) do
    field(:role, :string, default: "member")
  end
end
```

<!-- tabs-close -->

## Query Functions

<!-- tabs-open -->

### Plain Ecto

```elixir
users = Repo.all(User)

user = Repo.get(User, 1)

active_users = Repo.all_by(User, active: true)
```

### Drops.Relation

```elixir
users = MyApp.Users.all()

user = MyApp.Users.get(1)

active_users = MyApp.Users.all_by(active: true)
```

<!-- tabs-close -->

### Custom Query Definition

<!-- tabs-open -->

### Plain Ecto

```elixir
defmodule MyApp.Users do
  import Ecto.Query

  def active(query) do
    from u in query, where: u.active == true
  end

  def by_role(query, role) do
    from u in query, where: u.role == ^role
  end

  def order(query, field) do
    from u in query, order_by: ^field
  end
end

User
  |> MyApp.Users.active()
  |> MyApp.Users.by_role("member")
  |> MyApp.Users.order_by(:name)
  |> Repo.all()
```

### Drops.Relation

```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true)

  defquery active() do
    from(u in relation(), where: u.active == true)
  end

  defquery by_role(role) do
    from(u in relation(), where: u.role == ^role)
  end
end

MyApp.Users.active()
  |> MyApp.Users.by_role("member")
  |> MyApp.Users.order(:name)
  |> Repo.all()
```

<!-- tabs-close -->
