{"digest": "empty", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "primary_key": true, "check_constraints": [], "foreign_key": false, "index_name": null, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": false, "index_name": "posts_title_index", "nullable": false}, "name": ["atom", "title"], "type": ["atom", "string"], "source": ["atom", "title"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": false, "index_name": null, "nullable": true}, "name": ["atom", "body"], "type": ["atom", "string"], "source": ["atom", "body"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": false, "index_name": "posts_published_index", "nullable": true}, "name": ["atom", "published"], "type": ["atom", "boolean"], "source": ["atom", "published"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 0, "index": false, "type": ["atom", "integer"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": false, "index_name": null, "nullable": true}, "name": ["atom", "view_count"], "type": ["atom", "integer"], "source": ["atom", "view_count"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": true, "index_name": "posts_user_id_index", "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": ["atom", "user_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": false, "index_name": null, "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": false, "index_name": null, "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "posts"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "primary_key": true, "check_constraints": [], "foreign_key": false, "index_name": null, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [{"attributes": {"field": ["atom", "user_id"], "references_field": ["atom", "id"], "references_table": ["atom", "users"]}, "__struct__": "ForeignKey"}], "indices": [{"attributes": {"name": ["atom", "posts_title_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": false, "index_name": "posts_title_index", "nullable": false}, "name": ["atom", "title"], "type": ["atom", "string"], "source": ["atom", "title"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "posts_published_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": false, "index_name": "posts_published_index", "nullable": true}, "name": ["atom", "published"], "type": ["atom", "boolean"], "source": ["atom", "published"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "posts_user_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "primary_key": false, "check_constraints": [], "foreign_key": true, "index_name": "posts_user_id_index", "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": ["atom", "user_id"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}