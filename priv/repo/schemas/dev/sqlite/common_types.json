{"digest": "96F3CC1D80C93DB0BDEB0441005229B722879915FE3C2176663439450112F982", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "string_field"], "type": ["atom", "string"], "source": ["atom", "string_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "integer_field"], "type": ["atom", "integer"], "source": ["atom", "integer_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "text_field"], "type": ["atom", "string"], "source": ["atom", "text_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "binary"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "binary_field"], "type": ["atom", "binary"], "source": ["atom", "binary_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "array_with_string_member_field"], "type": ["atom", "string"], "source": ["atom", "array_with_string_member_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": [], "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "array_with_string_member_and_default"], "type": ["tuple", [["atom", "array"], ["atom", "any"]]], "source": ["atom", "array_with_string_member_and_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "array_with_jsonb_member_field"], "type": ["atom", "string"], "source": ["atom", "array_with_jsonb_member_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": [], "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "array_with_jsonb_member_and_default"], "type": ["tuple", [["atom", "array"], ["atom", "any"]]], "source": ["atom", "array_with_jsonb_member_and_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "map_field"], "type": ["atom", "string"], "source": ["atom", "map_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": {}, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "map_with_default"], "type": ["atom", "map"], "source": ["atom", "map_with_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "jsonb"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "jsonb_field"], "type": ["atom", "map"], "source": ["atom", "jsonb_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": {}, "index": false, "type": ["atom", "jsonb"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "jsonb_with_empty_map_default"], "type": ["atom", "map"], "source": ["atom", "jsonb_with_empty_map_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": [], "index": false, "type": ["atom", "jsonb"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "jsonb_with_empty_list_default"], "type": ["tuple", [["atom", "array"], ["atom", "any"]]], "source": ["atom", "jsonb_with_empty_list_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "default_value", "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "string_with_default"], "type": ["atom", "string"], "source": ["atom", "string_with_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 42, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "integer_with_default"], "type": ["atom", "integer"], "source": ["atom", "integer_with_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "required_string"], "type": ["atom", "string"], "source": ["atom", "required_string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "optional_string"], "type": ["atom", "string"], "source": ["atom", "optional_string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "common_types"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": []}, "__struct__": "<PERSON><PERSON><PERSON>"}}