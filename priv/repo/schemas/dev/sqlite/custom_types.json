{"digest": "96F3CC1D80C93DB0BDEB0441005229B722879915FE3C2176663439450112F982", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "binary"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "blob_field"], "type": ["atom", "binary"], "source": ["atom", "blob_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "float"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "real_field"], "type": ["atom", "float"], "source": ["atom", "real_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "numeric_field"], "type": ["atom", "decimal"], "source": ["atom", "numeric_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "boolean_field"], "type": ["atom", "integer"], "source": ["atom", "boolean_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": false, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "boolean_false_default"], "type": ["atom", "boolean"], "source": ["atom", "boolean_false_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "boolean_true_default"], "type": ["atom", "boolean"], "source": ["atom", "boolean_true_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "date_field"], "type": ["atom", "string"], "source": ["atom", "date_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "time_field"], "type": ["atom", "string"], "source": ["atom", "time_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "datetime_field"], "type": ["atom", "string"], "source": ["atom", "datetime_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 0, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "integer_zero_default"], "type": ["atom", "integer"], "source": ["atom", "integer_zero_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 1, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "integer_one_default"], "type": ["atom", "integer"], "source": ["atom", "integer_one_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "sqlite_default", "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "text_with_default"], "type": ["atom", "string"], "source": ["atom", "text_with_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "decimal_field"], "type": ["atom", "decimal"], "source": ["atom", "decimal_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "float_field"], "type": ["atom", "decimal"], "source": ["atom", "float_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "double_field"], "type": ["atom", "decimal"], "source": ["atom", "double_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "varchar_field"], "type": ["atom", "string"], "source": ["atom", "varchar_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "char_field"], "type": ["atom", "string"], "source": ["atom", "char_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "clob_field"], "type": ["atom", "string"], "source": ["atom", "clob_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "json_field"], "type": ["atom", "string"], "source": ["atom", "json_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "required_text"], "type": ["atom", "string"], "source": ["atom", "required_text"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "optional_text"], "type": ["atom", "string"], "source": ["atom", "optional_text"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "score_field"], "type": ["atom", "integer"], "source": ["atom", "score_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "function_default": true, "index_name": null}, "name": ["atom", "function_default"], "type": ["atom", "string"], "source": ["atom", "function_default"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "custom_types"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": []}, "__struct__": "<PERSON><PERSON><PERSON>"}}