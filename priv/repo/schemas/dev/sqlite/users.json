{"digest": "96F3CC1D80C93DB0BDEB0441005229B722879915FE3C2176663439450112F982", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "users_name_index"}, "name": ["atom", "name"], "type": ["atom", "string"], "source": ["atom", "name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "users_email_index"}, "name": ["atom", "email"], "type": ["atom", "string"], "source": ["atom", "email"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "users_name_age_index"}, "name": ["atom", "age"], "type": ["atom", "integer"], "source": ["atom", "age"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": false, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "active"], "type": ["atom", "boolean"], "source": ["atom", "active"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "users"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": [{"attributes": {"name": ["atom", "users_name_age_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "users_name_index"}, "name": ["atom", "name"], "type": ["atom", "string"], "source": ["atom", "name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "users_name_age_index"}, "name": ["atom", "age"], "type": ["atom", "integer"], "source": ["atom", "age"]}, "__struct__": "Field"}], "composite": true, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_name_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "users_name_index"}, "name": ["atom", "name"], "type": ["atom", "string"], "source": ["atom", "name"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_email_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "users_email_index"}, "name": ["atom", "email"], "type": ["atom", "string"], "source": ["atom", "email"]}, "__struct__": "Field"}], "composite": false, "unique": true}, "__struct__": "Index"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}