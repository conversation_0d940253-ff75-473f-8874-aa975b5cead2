{"digest": "96F3CC1D80C93DB0BDEB0441005229B722879915FE3C2176663439450112F982", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "active", "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_status_index"}, "name": ["atom", "status"], "type": ["atom", "string"], "source": ["atom", "status"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "description"], "type": ["atom", "string"], "source": ["atom", "description"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_name_priority_index"}, "name": ["atom", "name"], "type": ["atom", "string"], "source": ["atom", "name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 1, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_name_priority_index"}, "name": ["atom", "priority"], "type": ["atom", "integer"], "source": ["atom", "priority"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 1, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "is_enabled"], "type": ["atom", "integer"], "source": ["atom", "is_enabled"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": ["score >= 0 AND score <= 100"], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "score"], "type": ["atom", "integer"], "source": ["atom", "score"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "metadata_test"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": [{"attributes": {"name": ["atom", "metadata_test_name_priority_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_name_priority_index"}, "name": ["atom", "name"], "type": ["atom", "string"], "source": ["atom", "name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 1, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_name_priority_index"}, "name": ["atom", "priority"], "type": ["atom", "integer"], "source": ["atom", "priority"]}, "__struct__": "Field"}], "composite": true, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "metadata_test_status_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": "active", "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_status_index"}, "name": ["atom", "status"], "type": ["atom", "string"], "source": ["atom", "status"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}