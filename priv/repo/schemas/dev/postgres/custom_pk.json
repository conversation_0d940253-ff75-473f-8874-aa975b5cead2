{"digest": "768B78E83F263BD308AD1C21662B0A2E185F269FAB20172CC7D38CD6241B2879", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "uuid"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "uuid"], "type": ["atom", "binary_id"], "source": ["atom", "uuid"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "name"], "type": ["atom", "string"], "source": ["atom", "name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "custom_pk"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "uuid"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "uuid"], "type": ["atom", "binary_id"], "source": ["atom", "uuid"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": []}, "__struct__": "<PERSON><PERSON><PERSON>"}}