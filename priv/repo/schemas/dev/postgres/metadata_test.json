{"digest": "768B78E83F263BD308AD1C21662B0A2E185F269FAB20172CC7D38CD6241B2879", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "active", "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": ["CHECK (((status)::text = ANY ((ARRAY['active'::character varying, 'inactive'::character varying, 'pending'::character varying])::text[])))"], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_status_index"}, "name": ["atom", "status"], "type": ["atom", "string"], "source": ["atom", "status"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "description"], "type": ["atom", "string"], "source": ["atom", "description"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_name_priority_index"}, "name": ["atom", "name"], "type": ["atom", "string"], "source": ["atom", "name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 1, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_name_priority_index"}, "name": ["atom", "priority"], "type": ["atom", "integer"], "source": ["atom", "priority"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "index": false, "type": ["atom", "boolean"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "is_enabled"], "type": ["atom", "boolean"], "source": ["atom", "is_enabled"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": ["CHECK (((score >= 0) AND (score <= 100)))"], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "score"], "type": ["atom", "integer"], "source": ["atom", "score"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "metadata_test"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": [{"attributes": {"name": ["atom", "metadata_test_name_priority_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_name_priority_index"}, "name": ["atom", "name"], "type": ["atom", "string"], "source": ["atom", "name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 1, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_name_priority_index"}, "name": ["atom", "priority"], "type": ["atom", "integer"], "source": ["atom", "priority"]}, "__struct__": "Field"}], "composite": true, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "metadata_test_status_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": "active", "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": ["CHECK (((status)::text = ANY ((ARRAY['active'::character varying, 'inactive'::character varying, 'pending'::character varying])::text[])))"], "primary_key": false, "foreign_key": false, "index_name": "metadata_test_status_index"}, "name": ["atom", "status"], "type": ["atom", "string"], "source": ["atom", "status"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}