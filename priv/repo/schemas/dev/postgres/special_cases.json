{"digest": "768B78E83F263BD308AD1C21662B0A2E185F269FAB20172CC7D38CD6241B2879", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "special_cases_user_id_index"}, "name": ["atom", "user_id"], "type": ["atom", "id"], "source": ["atom", "user_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": null}, "name": ["atom", "parent_id"], "type": ["atom", "id"], "source": ["atom", "parent_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "special_cases_required_field_index"}, "name": ["atom", "required_field"], "type": ["atom", "string"], "source": ["atom", "required_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "optional_field"], "type": ["atom", "string"], "source": ["atom", "optional_field"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "default_value", "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "default_string"], "type": ["atom", "string"], "source": ["atom", "default_string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 42, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "default_integer"], "type": ["atom", "integer"], "source": ["atom", "default_integer"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "index": false, "type": ["atom", "boolean"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "default_boolean"], "type": ["atom", "boolean"], "source": ["atom", "default_boolean"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "special_cases"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [{"attributes": {"field": ["atom", "parent_id"], "references_table": ["atom", "special_cases"], "references_field": ["atom", "id"]}, "__struct__": "ForeignKey"}, {"attributes": {"field": ["atom", "user_id"], "references_table": ["atom", "users"], "references_field": ["atom", "id"]}, "__struct__": "ForeignKey"}], "indices": [{"attributes": {"name": ["atom", "special_cases_required_field_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "special_cases_required_field_index"}, "name": ["atom", "required_field"], "type": ["atom", "string"], "source": ["atom", "required_field"]}, "__struct__": "Field"}], "composite": false, "unique": true}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "special_cases_user_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "special_cases_user_id_index"}, "name": ["atom", "user_id"], "type": ["atom", "id"], "source": ["atom", "user_id"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}