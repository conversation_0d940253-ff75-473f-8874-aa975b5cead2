{"digest": "768B78E83F263BD308AD1C21662B0A2E185F269FAB20172CC7D38CD6241B2879", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "user_groups_user_id_group_id_index"}, "name": ["atom", "user_id"], "type": ["atom", "id"], "source": ["atom", "user_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "user_groups_user_id_group_id_index"}, "name": ["atom", "group_id"], "type": ["atom", "id"], "source": ["atom", "group_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "user_groups"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [{"attributes": {"field": ["atom", "group_id"], "references_table": ["atom", "groups"], "references_field": ["atom", "id"]}, "__struct__": "ForeignKey"}, {"attributes": {"field": ["atom", "user_id"], "references_table": ["atom", "users"], "references_field": ["atom", "id"]}, "__struct__": "ForeignKey"}], "indices": [{"attributes": {"name": ["atom", "user_groups_user_id_group_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "user_groups_user_id_group_id_index"}, "name": ["atom", "user_id"], "type": ["atom", "id"], "source": ["atom", "user_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "user_groups_user_id_group_id_index"}, "name": ["atom", "group_id"], "type": ["atom", "id"], "source": ["atom", "group_id"]}, "__struct__": "Field"}], "composite": true, "unique": true}, "__struct__": "Index"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}