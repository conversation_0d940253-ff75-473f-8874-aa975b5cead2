{"digest": "768B78E83F263BD308AD1C21662B0A2E185F269FAB20172CC7D38CD6241B2879", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "smallint_type"], "type": ["atom", "integer"], "source": ["atom", "smallint_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "int2_type"], "type": ["atom", "integer"], "source": ["atom", "int2_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "postgres_types_integer_type_index"}, "name": ["atom", "integer_type"], "type": ["atom", "integer"], "source": ["atom", "integer_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "int_type"], "type": ["atom", "integer"], "source": ["atom", "int_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "int4_type"], "type": ["atom", "integer"], "source": ["atom", "int4_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "bigint_type"], "type": ["atom", "integer"], "source": ["atom", "bigint_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "int8_type"], "type": ["atom", "integer"], "source": ["atom", "int8_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "serial_type"], "type": ["atom", "integer"], "source": ["atom", "serial_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "bigserial_type"], "type": ["atom", "integer"], "source": ["atom", "bigserial_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "float"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "real_type"], "type": ["atom", "float"], "source": ["atom", "real_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "float"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "float4_type"], "type": ["atom", "float"], "source": ["atom", "float4_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "float"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "double_precision_type"], "type": ["atom", "float"], "source": ["atom", "double_precision_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "float"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "float8_type"], "type": ["atom", "float"], "source": ["atom", "float8_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "numeric_type"], "type": ["atom", "decimal"], "source": ["atom", "numeric_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "decimal_type"], "type": ["atom", "decimal"], "source": ["atom", "decimal_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "money_type"], "type": ["atom", "decimal"], "source": ["atom", "money_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "character_varying_type"], "type": ["atom", "string"], "source": ["atom", "character_varying_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "postgres_types_varchar_type_text_type_index"}, "name": ["atom", "varchar_type"], "type": ["atom", "string"], "source": ["atom", "varchar_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "character_type"], "type": ["atom", "string"], "source": ["atom", "character_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "char_type"], "type": ["atom", "string"], "source": ["atom", "char_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "postgres_types_varchar_type_text_type_index"}, "name": ["atom", "text_type"], "type": ["atom", "string"], "source": ["atom", "text_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "name_type"], "type": ["atom", "string"], "source": ["atom", "name_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "date"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "date_type"], "type": ["atom", "date"], "source": ["atom", "date_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "time"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "time_type"], "type": ["atom", "time"], "source": ["atom", "time_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "time"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "time_with_tz_type"], "type": ["atom", "time"], "source": ["atom", "time_with_tz_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "timestamp_type"], "type": ["atom", "naive_datetime"], "source": ["atom", "timestamp_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "utc_datetime"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "timestamp_with_tz_type"], "type": ["atom", "utc_datetime"], "source": ["atom", "timestamp_with_tz_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "binary"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "bytea_type"], "type": ["atom", "binary"], "source": ["atom", "bytea_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "boolean"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "boolean_type"], "type": ["atom", "boolean"], "source": ["atom", "boolean_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "jsonb"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "json_type"], "type": ["atom", "map"], "source": ["atom", "json_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "jsonb"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "jsonb_type"], "type": ["atom", "map"], "source": ["atom", "jsonb_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "uuid"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "postgres_types_uuid_type_index"}, "name": ["atom", "uuid_type"], "type": ["atom", "binary"], "source": ["atom", "uuid_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inet_type"], "type": ["atom", "string"], "source": ["atom", "inet_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "cidr_type"], "type": ["atom", "string"], "source": ["atom", "cidr_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "macaddr_type"], "type": ["atom", "string"], "source": ["atom", "macaddr_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "xml_type"], "type": ["atom", "string"], "source": ["atom", "xml_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "postgres_types"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": [{"attributes": {"name": ["atom", "postgres_types_integer_type_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "postgres_types_integer_type_index"}, "name": ["atom", "integer_type"], "type": ["atom", "integer"], "source": ["atom", "integer_type"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "postgres_types_uuid_type_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "uuid"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "postgres_types_uuid_type_index"}, "name": ["atom", "uuid_type"], "type": ["atom", "binary"], "source": ["atom", "uuid_type"]}, "__struct__": "Field"}], "composite": false, "unique": true}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "postgres_types_varchar_type_text_type_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "postgres_types_varchar_type_text_type_index"}, "name": ["atom", "varchar_type"], "type": ["atom", "string"], "source": ["atom", "varchar_type"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "postgres_types_varchar_type_text_type_index"}, "name": ["atom", "text_type"], "type": ["atom", "string"], "source": ["atom", "text_type"]}, "__struct__": "Field"}], "composite": true, "unique": false}, "__struct__": "Index"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}