{"digest": "768B78E83F263BD308AD1C21662B0A2E185F269FAB20172CC7D38CD6241B2879", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["tuple", [["atom", "array"], ["atom", "integer"]]], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "integer_array"], "type": ["tuple", [["atom", "array"], ["atom", "integer"]]], "source": ["atom", "integer_array"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["tuple", [["atom", "array"], ["atom", "string"]]], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "text_array"], "type": ["tuple", [["atom", "array"], ["atom", "string"]]], "source": ["atom", "text_array"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["tuple", [["atom", "array"], ["atom", "boolean"]]], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "boolean_array"], "type": ["tuple", [["atom", "array"], ["atom", "boolean"]]], "source": ["atom", "boolean_array"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["tuple", [["atom", "array"], ["atom", "uuid"]]], "embed": false, "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "uuid_array"], "type": ["tuple", [["atom", "array"], ["atom", "binary"]]], "source": ["atom", "uuid_array"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "postgres_array_types"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "embed": false, "nullable": false, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": []}, "__struct__": "<PERSON><PERSON><PERSON>"}}